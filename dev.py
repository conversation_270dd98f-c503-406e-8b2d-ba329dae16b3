#!/usr/bin/env python3
"""Development helper script for CV Chat API."""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path
from typing import Optional


class DevHelper:
    """Development helper for CV Chat API."""
    
    def __init__(self):
        """Initialize development helper."""
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
            print("   Please use Python 3.8 or higher")
            return False
    
    def create_venv(self) -> bool:
        """Create virtual environment."""
        if self.venv_path.exists():
            print("✅ Virtual environment already exists")
            return True
        
        print("🔧 Creating virtual environment...")
        try:
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], check=True)
            print("✅ Virtual environment created successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False
    
    def get_pip_path(self) -> str:
        """Get pip executable path for the virtual environment."""
        if os.name == 'nt':  # Windows
            return str(self.venv_path / "Scripts" / "pip")
        else:  # Unix/Linux/macOS
            return str(self.venv_path / "bin" / "pip")
    
    def get_python_path(self) -> str:
        """Get Python executable path for the virtual environment."""
        if os.name == 'nt':  # Windows
            return str(self.venv_path / "Scripts" / "python")
        else:  # Unix/Linux/macOS
            return str(self.venv_path / "bin" / "python")
    
    def install_dependencies(self) -> bool:
        """Install project dependencies."""
        if not self.requirements_file.exists():
            print(f"❌ Requirements file not found: {self.requirements_file}")
            return False
        
        print("📦 Installing dependencies...")
        try:
            pip_path = self.get_pip_path()
            subprocess.run([
                pip_path, "install", "-r", str(self.requirements_file)
            ], check=True)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    def check_env_file(self) -> bool:
        """Check if .env file exists and create from example if not."""
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if env_file.exists():
            print("✅ .env file exists")
            return True
        
        if env_example.exists():
            print("🔧 Creating .env file from .env.example...")
            try:
                with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                    dst.write(src.read())
                print("✅ .env file created")
                print("⚠️  Please update .env file with your actual values (especially OPENAI_API_KEY)")
                return True
            except Exception as e:
                print(f"❌ Failed to create .env file: {e}")
                return False
        else:
            print("❌ .env.example file not found")
            return False
    
    def create_directories(self) -> bool:
        """Create necessary directories."""
        directories = ["cv", "cv_index", "logs"]
        
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                print(f"🔧 Creating directory: {dir_name}")
                dir_path.mkdir(parents=True, exist_ok=True)
            else:
                print(f"✅ Directory exists: {dir_name}")
        
        return True
    
    def run_server(self, host: str = "0.0.0.0", port: int = 8000, reload: bool = True) -> None:
        """Run the development server."""
        python_path = self.get_python_path()
        
        print(f"🚀 Starting development server on {host}:{port}")
        print(f"   Reload: {reload}")
        print("   Press Ctrl+C to stop")
        
        cmd = [
            python_path, "-m", "uvicorn", 
            "main:app", 
            "--host", host, 
            "--port", str(port)
        ]
        
        if reload:
            cmd.append("--reload")
        
        try:
            subprocess.run(cmd, cwd=str(self.project_root))
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
    
    def run_tests(self) -> bool:
        """Run API tests."""
        python_path = self.get_python_path()
        test_script = self.project_root / "test_api.py"
        
        if not test_script.exists():
            print(f"❌ Test script not found: {test_script}")
            return False
        
        print("🧪 Running API tests...")
        try:
            subprocess.run([python_path, str(test_script)], check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Tests failed: {e}")
            return False
    
    def setup(self) -> bool:
        """Setup development environment."""
        print("🔧 Setting up development environment...")
        
        if not self.check_python_version():
            return False
        
        if not self.create_venv():
            return False
        
        if not self.install_dependencies():
            return False
        
        if not self.check_env_file():
            return False
        
        if not self.create_directories():
            return False
        
        print("\n🎉 Development environment setup complete!")
        print("\nNext steps:")
        print("1. Update .env file with your OpenAI API key")
        print("2. Add your CV files to the 'cv' directory")
        print("3. Run 'python dev.py run' to start the server")
        print("4. Run 'python dev.py test' to test the API")
        
        return True
    
    def clean(self) -> bool:
        """Clean development environment."""
        print("🧹 Cleaning development environment...")
        
        # Remove virtual environment
        if self.venv_path.exists():
            print("🗑️  Removing virtual environment...")
            import shutil
            shutil.rmtree(self.venv_path)
            print("✅ Virtual environment removed")
        
        # Remove generated index
        index_path = self.project_root / "cv_index"
        if index_path.exists():
            print("🗑️  Removing CV index...")
            import shutil
            shutil.rmtree(index_path)
            print("✅ CV index removed")
        
        # Remove logs
        logs_path = self.project_root / "logs"
        if logs_path.exists():
            print("🗑️  Removing logs...")
            import shutil
            shutil.rmtree(logs_path)
            print("✅ Logs removed")
        
        print("✅ Cleanup complete")
        return True


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="CV Chat API Development Helper")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Setup command
    subparsers.add_parser("setup", help="Setup development environment")
    
    # Run command
    run_parser = subparsers.add_parser("run", help="Run development server")
    run_parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    run_parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    run_parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    
    # Test command
    subparsers.add_parser("test", help="Run API tests")
    
    # Clean command
    subparsers.add_parser("clean", help="Clean development environment")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    dev_helper = DevHelper()
    
    if args.command == "setup":
        dev_helper.setup()
    elif args.command == "run":
        dev_helper.run_server(
            host=args.host,
            port=args.port,
            reload=not args.no_reload
        )
    elif args.command == "test":
        dev_helper.run_tests()
    elif args.command == "clean":
        dev_helper.clean()


if __name__ == "__main__":
    main()