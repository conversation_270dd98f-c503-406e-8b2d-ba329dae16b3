#!/usr/bin/env python3
"""Test script for CV Chat API."""

import asyncio
import json
import requests
from typing import Dict, Any


class CVChatAPITester:
    """Test client for CV Chat API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the tester with base URL."""
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> Dict[str, Any]:
        """Test health endpoint."""
        print("\n🔍 Testing health endpoint...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            print(f"   Checks: {json.dumps(data['checks'], indent=2)}")
            return data
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return {}
    
    def test_root(self) -> Dict[str, Any]:
        """Test root endpoint."""
        print("\n🔍 Testing root endpoint...")
        try:
            response = self.session.get(f"{self.base_url}/")
            response.raise_for_status()
            data = response.json()
            print(f"✅ Root endpoint working: {data['name']}")
            print(f"   Version: {data['version']}")
            return data
        except Exception as e:
            print(f"❌ Root endpoint failed: {e}")
            return {}
    
    def test_suggested_questions(self) -> Dict[str, Any]:
        """Test suggested questions endpoint."""
        print("\n🔍 Testing suggested questions...")
        try:
            response = self.session.get(f"{self.base_url}/suggested-questions")
            response.raise_for_status()
            data = response.json()
            print(f"✅ Got {len(data['questions'])} suggested questions")
            for i, question in enumerate(data['questions'][:3], 1):
                print(f"   {i}. {question}")
            return data
        except Exception as e:
            print(f"❌ Suggested questions failed: {e}")
            return {}
    
    def test_chat(self, message: str = "What programming languages do you know?") -> Dict[str, Any]:
        """Test chat endpoint."""
        print(f"\n🔍 Testing chat with message: '{message}'")
        try:
            payload = {"message": message}
            response = self.session.post(
                f"{self.base_url}/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            data = response.json()
            print(f"✅ Chat response received")
            print(f"   Response: {data['response'][:100]}...")
            print(f"   Sources: {data['sources']}")
            print(f"   Conversation ID: {data['conversation_id']}")
            return data
        except Exception as e:
            print(f"❌ Chat failed: {e}")
            if hasattr(e, 'response'):
                try:
                    error_data = e.response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Response text: {e.response.text}")
            return {}
    
    def test_update_cv(self) -> Dict[str, Any]:
        """Test CV update endpoint."""
        print("\n🔍 Testing CV index update...")
        try:
            response = self.session.post(f"{self.base_url}/update-cv")
            response.raise_for_status()
            data = response.json()
            print(f"✅ CV update completed: {data['status']}")
            print(f"   Files processed: {data['files_processed']}")
            print(f"   Message: {data['message']}")
            return data
        except Exception as e:
            print(f"❌ CV update failed: {e}")
            if hasattr(e, 'response'):
                try:
                    error_data = e.response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Response text: {e.response.text}")
            return {}
    
    def run_all_tests(self) -> None:
        """Run all API tests."""
        print("🚀 Starting CV Chat API Tests")
        print(f"   Base URL: {self.base_url}")
        
        # Test basic endpoints
        self.test_root()
        health_data = self.test_health()
        
        # Check if system is healthy before proceeding
        if health_data.get('status') != 'healthy':
            print("\n⚠️  System not healthy, some tests may fail")
        
        self.test_suggested_questions()
        
        # Test chat functionality
        test_messages = [
            "What programming languages do you know?",
            "Tell me about your work experience",
            "What are your key skills?"
        ]
        
        for message in test_messages:
            self.test_chat(message)
        
        # Test CV update (be careful with this in production)
        # self.test_update_cv()
        
        print("\n🎉 All tests completed!")


def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test CV Chat API")
    parser.add_argument(
        "--url",
        default="http://localhost:8000",
        help="Base URL for the API (default: http://localhost:8000)"
    )
    parser.add_argument(
        "--message",
        help="Custom message to test chat endpoint"
    )
    parser.add_argument(
        "--endpoint",
        choices=["health", "root", "chat", "questions", "update", "all"],
        default="all",
        help="Specific endpoint to test (default: all)"
    )
    
    args = parser.parse_args()
    
    tester = CVChatAPITester(args.url)
    
    if args.endpoint == "health":
        tester.test_health()
    elif args.endpoint == "root":
        tester.test_root()
    elif args.endpoint == "chat":
        message = args.message or "What programming languages do you know?"
        tester.test_chat(message)
    elif args.endpoint == "questions":
        tester.test_suggested_questions()
    elif args.endpoint == "update":
        tester.test_update_cv()
    else:
        tester.run_all_tests()


if __name__ == "__main__":
    main()