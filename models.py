"""Pydantic models for CV Chat API."""

from typing import List, Optional
from pydantic import BaseModel, Field
from uuid import uuid4


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    
    message: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="User's question about the CV"
    )
    conversation_id: Optional[str] = Field(
        default=None,
        description="Optional conversation ID for context"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "What programming languages do you know?",
                "conversation_id": "550e8400-e29b-41d4-a716-************"
            }
        }


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    
    response: str = Field(
        ...,
        description="AI-generated response based on CV content"
    )
    sources: List[str] = Field(
        default_factory=list,
        description="Source references from the CV"
    )
    conversation_id: str = Field(
        default_factory=lambda: str(uuid4()),
        description="Unique conversation identifier"
    )
    confidence: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Confidence score of the response"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "response": "I have experience with Python, JavaScript, React, and Node.js...",
                "sources": ["page 1, Experience section", "page 2, Skills section"],
                "conversation_id": "550e8400-e29b-41d4-a716-************",
                "confidence": 0.95
            }
        }


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(
        ...,
        description="Error type or category"
    )
    message: str = Field(
        ...,
        description="Human-readable error message"
    )
    details: Optional[dict] = Field(
        default=None,
        description="Optional additional error details"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "validation_error",
                "message": "The message field is required and cannot be empty",
                "details": {"field": "message", "code": "required"}
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str = Field(
        default="healthy",
        description="Service health status"
    )
    version: str = Field(
        default="1.0.0",
        description="API version"
    )
    timestamp: str = Field(
        ...,
        description="Current timestamp"
    )
    checks: dict = Field(
        default_factory=dict,
        description="Individual health check results"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": "2024-01-01T12:00:00Z",
                "checks": {
                    "openai_api": "connected",
                    "cv_index": "loaded",
                    "cv_files": "found"
                }
            }
        }


class SuggestedQuestionsResponse(BaseModel):
    """Suggested questions response model."""
    
    questions: List[str] = Field(
        ...,
        description="List of suggested questions about the CV"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "questions": [
                    "What programming languages do you know?",
                    "Tell me about your work experience",
                    "What are your key skills?",
                    "What projects have you worked on?",
                    "What is your educational background?"
                ]
            }
        }


class UpdateCVResponse(BaseModel):
    """Update CV index response model."""
    
    status: str = Field(
        ...,
        description="Update operation status"
    )
    message: str = Field(
        ...,
        description="Update operation message"
    )
    files_processed: int = Field(
        default=0,
        description="Number of CV files processed"
    )
    timestamp: str = Field(
        ...,
        description="Update timestamp"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "success",
                "message": "CV index updated successfully",
                "files_processed": 1,
                "timestamp": "2024-01-01T12:00:00Z"
            }
        }