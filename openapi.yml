openapi: 3.0.3
info:
  title: CV Chat API
  description: |
    RAG-powered API for chatting about CV/resume content using LlamaIndex and OpenAI.
    
    This API allows users to:
    - Ask questions about CV content and get AI-powered responses
    - Get suggested questions for CV exploration
    - Update the CV index when new files are added
    - Monitor system health and status
    
    The system uses Retrieval-Augmented Generation (RAG) to provide accurate,
    source-attributed responses based on CV content.
  version: 1.0.0
  contact:
    name: CV Chat API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000
    description: Local development server
  - url: https://your-production-domain.com
    description: Production server

paths:
  /:
    get:
      summary: API Information
      description: Get basic information about the CV Chat API
      operationId: getApiInfo
      tags:
        - General
      responses:
        '200':
          description: API information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    example: "CV Chat API"
                  version:
                    type: string
                    example: "1.0.0"
                  description:
                    type: string
                    example: "RAG-powered API for chatting about CV/resume content"
                  docs_url:
                    type: string
                    example: "/docs"

  /health:
    get:
      summary: Health Check
      description: Check the health status of the API and its components
      operationId: healthCheck
      tags:
        - Health
      responses:
        '200':
          description: Health status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: Service unavailable - health check failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chat:
    post:
      summary: Chat with CV
      description: |
        Ask questions about the CV content and get AI-powered responses.
        
        The system uses RAG (Retrieval-Augmented Generation) to:
        1. Search relevant CV content based on your question
        2. Generate contextual responses using OpenAI GPT-4
        3. Provide source attribution for transparency
      operationId: chat
      tags:
        - Chat
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
            examples:
              programming_languages:
                summary: Ask about programming languages
                value:
                  message: "What programming languages do you know?"
              work_experience:
                summary: Ask about work experience
                value:
                  message: "Tell me about your work experience"
                  conversation_id: "550e8400-e29b-41d4-a716-************"
              skills:
                summary: Ask about skills
                value:
                  message: "What are your key technical skills?"
      responses:
        '200':
          description: Chat response generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
        '400':
          description: Bad request - invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /suggested-questions:
    get:
      summary: Get Suggested Questions
      description: |
        Get a list of suggested questions to ask about the CV.
        
        These questions are designed to help users explore different aspects
        of the CV content, including skills, experience, education, and projects.
      operationId: getSuggestedQuestions
      tags:
        - Chat
      responses:
        '200':
          description: Suggested questions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestedQuestionsResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /update-cv:
    post:
      summary: Update CV Index
      description: |
        Refresh the CV index by reprocessing all CV files in the configured directory.
        
        This endpoint should be called when:
        - New CV files are added to the system
        - Existing CV files are modified
        - The index becomes corrupted or outdated
        
        **Note:** This operation may take some time depending on the number and size of CV files.
      operationId: updateCVIndex
      tags:
        - Management
      responses:
        '200':
          description: CV index updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCVResponse'
        '500':
          description: Internal server error during index update
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    ChatRequest:
      type: object
      required:
        - message
      properties:
        message:
          type: string
          minLength: 1
          maxLength: 1000
          description: User's question about the CV
          example: "What programming languages do you know?"
        conversation_id:
          type: string
          format: uuid
          description: Optional conversation ID for context
          example: "550e8400-e29b-41d4-a716-************"
      example:
        message: "What programming languages do you know?"
        conversation_id: "550e8400-e29b-41d4-a716-************"

    ChatResponse:
      type: object
      required:
        - response
        - sources
        - conversation_id
      properties:
        response:
          type: string
          description: AI-generated response based on CV content
          example: "I have experience with Python, JavaScript, React, and Node.js. I've used Python extensively for backend development with frameworks like Django and FastAPI, while JavaScript and React have been my go-to technologies for frontend development."
        sources:
          type: array
          items:
            type: string
          description: Source references from the CV
          example: ["page 1, Experience section", "page 2, Skills section"]
        conversation_id:
          type: string
          format: uuid
          description: Unique conversation identifier
          example: "550e8400-e29b-41d4-a716-************"
        confidence:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
          description: Confidence score of the response
          example: 0.95
      example:
        response: "I have experience with Python, JavaScript, React, and Node.js..."
        sources: ["page 1, Experience section", "page 2, Skills section"]
        conversation_id: "550e8400-e29b-41d4-a716-************"
        confidence: 0.95

    HealthResponse:
      type: object
      required:
        - status
        - version
        - timestamp
        - checks
      properties:
        status:
          type: string
          enum: ["healthy", "degraded", "unhealthy"]
          description: Service health status
          example: "healthy"
        version:
          type: string
          description: API version
          example: "1.0.0"
        timestamp:
          type: string
          format: date-time
          description: Current timestamp in ISO 8601 format
          example: "2024-01-01T12:00:00Z"
        checks:
          type: object
          description: Individual health check results
          additionalProperties:
            type: string
          example:
            openai_api: "connected"
            cv_index: "loaded"
            cv_files: "found"
      example:
        status: "healthy"
        version: "1.0.0"
        timestamp: "2024-01-01T12:00:00Z"
        checks:
          openai_api: "connected"
          cv_index: "loaded"
          cv_files: "found"

    SuggestedQuestionsResponse:
      type: object
      required:
        - questions
      properties:
        questions:
          type: array
          items:
            type: string
          description: List of suggested questions about the CV
          example:
            - "What programming languages do you know?"
            - "Tell me about your work experience"
            - "What are your key skills?"
            - "What projects have you worked on?"
            - "What is your educational background?"
      example:
        questions:
          - "What programming languages do you know?"
          - "Tell me about your work experience"
          - "What are your key skills?"
          - "What projects have you worked on?"
          - "What is your educational background?"

    UpdateCVResponse:
      type: object
      required:
        - status
        - message
        - files_processed
        - timestamp
      properties:
        status:
          type: string
          enum: ["success", "partial", "failed"]
          description: Update operation status
          example: "success"
        message:
          type: string
          description: Update operation message
          example: "CV index updated successfully"
        files_processed:
          type: integer
          minimum: 0
          description: Number of CV files processed
          example: 1
        timestamp:
          type: string
          format: date-time
          description: Update timestamp in ISO 8601 format
          example: "2024-01-01T12:00:00Z"
      example:
        status: "success"
        message: "CV index updated successfully"
        files_processed: 1
        timestamp: "2024-01-01T12:00:00Z"

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          description: Error type or category
          example: "validation_error"
        message:
          type: string
          description: Human-readable error message
          example: "The message field is required and cannot be empty"
        details:
          type: object
          description: Optional additional error details
          additionalProperties: true
          example:
            field: "message"
            code: "required"
      example:
        error: "validation_error"
        message: "The message field is required and cannot be empty"
        details:
          field: "message"
          code: "required"

tags:
  - name: General
    description: General API information and metadata
  - name: Health
    description: Health monitoring and status endpoints
  - name: Chat
    description: CV chat and question-answering functionality
  - name: Management
    description: System management and maintenance operations

externalDocs:
  description: Find more info about the CV Chat API
  url: https://github.com/your-username/cv-chat-api