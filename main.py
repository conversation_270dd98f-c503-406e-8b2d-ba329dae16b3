"""FastAPI application for CV Chat API."""

import logging
import asyncio
from datetime import datetime
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pythonjsonlogger import jsonlogger

from config import settings
from models import (
    ChatRequest,
    ChatResponse,
    ErrorResponse,
    HealthResponse,
    SuggestedQuestionsResponse,
    UpdateCVResponse
)
from cv_chat_system import cv_chat_system


# Configure logging
def setup_logging() -> None:
    """Setup structured logging for the application."""
    log_handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        "%(asctime)s %(name)s %(levelname)s %(message)s"
    )
    log_handler.setFormatter(formatter)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        handlers=[log_handler],
        force=True
    )
    
    # Set specific loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger = logging.getLogger(__name__)
    
    # Startup
    logger.info("Starting CV Chat API...")
    try:
        await cv_chat_system.initialize()
        logger.info("CV Chat API started successfully")
    except Exception as e:
        logger.error(f"Failed to start CV Chat API: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down CV Chat API...")


# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="CV Chat API",
    description="RAG-powered API for chatting about CV/resume content",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    logger.warning(f"HTTP exception: {exc.status_code} - {exc.detail}")
    
    error_response = ErrorResponse(
        error="http_error",
        message=exc.detail,
        details={"status_code": exc.status_code}
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception) -> JSONResponse:
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {type(exc).__name__}: {exc}")
    
    error_response = ErrorResponse(
        error="internal_error",
        message="An internal server error occurred",
        details={"type": type(exc).__name__}
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.model_dump()
    )


# API Routes
@app.get(
    "/health",
    response_model=HealthResponse,
    summary="Health Check",
    description="Check the health status of the API and its components"
)
async def health_check() -> HealthResponse:
    """Health check endpoint."""
    try:
        checks = await cv_chat_system.health_check()
        
        # Determine overall status
        status_value = "healthy"
        if any(check in ["failed", "not_loaded", "not_found", "not_ready"] for check in checks.values()):
            status_value = "degraded"
        if "error" in checks:
            status_value = "unhealthy"
        
        return HealthResponse(
            status=status_value,
            version="1.0.0",
            timestamp=datetime.utcnow().isoformat() + "Z",
            checks=checks
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Health check failed"
        )


@app.post(
    "/chat",
    response_model=ChatResponse,
    summary="Chat with CV",
    description="Ask questions about the CV content and get AI-powered responses"
)
async def chat(request: ChatRequest) -> ChatResponse:
    """Main chat endpoint for CV questions."""
    try:
        logger.info(f"Received chat request: {request.message[:100]}...")
        
        # Process the query
        response_text, sources = await cv_chat_system.query(
            message=request.message,
            conversation_id=request.conversation_id
        )
        
        # Create response
        chat_response = ChatResponse(
            response=response_text,
            sources=sources,
            conversation_id=request.conversation_id or "new-conversation"
        )
        
        logger.info(f"Chat response generated successfully")
        return chat_response
        
    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@app.get(
    "/suggested-questions",
    response_model=SuggestedQuestionsResponse,
    summary="Get Suggested Questions",
    description="Get a list of suggested questions to ask about the CV"
)
async def get_suggested_questions() -> SuggestedQuestionsResponse:
    """Get suggested questions for CV exploration."""
    try:
        questions = cv_chat_system.get_suggested_questions()
        
        return SuggestedQuestionsResponse(questions=questions)
        
    except Exception as e:
        logger.error(f"Failed to get suggested questions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get suggested questions"
        )


@app.post(
    "/update-cv",
    response_model=UpdateCVResponse,
    summary="Update CV Index",
    description="Refresh the CV index with latest documents from the CV folder"
)
async def update_cv_index() -> UpdateCVResponse:
    """Update the CV index with latest documents."""
    try:
        logger.info("Received CV index update request")
        
        # Update the index
        message, files_processed = await cv_chat_system.update_index()
        
        response = UpdateCVResponse(
            status="success",
            message=message,
            files_processed=files_processed,
            timestamp=datetime.utcnow().isoformat() + "Z"
        )
        
        logger.info(f"CV index updated successfully: {files_processed} files processed")
        return response
        
    except FileNotFoundError as e:
        logger.error(f"CV files not found: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="CV files not found in the specified directory"
        )
    except Exception as e:
        logger.error(f"CV index update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update CV index"
        )


# Root endpoint
@app.get(
    "/",
    summary="API Root",
    description="API information and status"
)
async def root() -> Dict[str, Any]:
    """Root endpoint with API information."""
    return {
        "name": "CV Chat API",
        "version": "1.0.0",
        "description": "RAG-powered API for chatting about CV/resume content",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "endpoints": {
            "chat": "/chat",
            "health": "/health",
            "suggested_questions": "/suggested-questions",
            "update_cv": "/update-cv",
            "docs": "/docs"
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.api_port,
        reload=False,
        log_level=settings.log_level.lower()
    )