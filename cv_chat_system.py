"""Core RAG system for CV chat using LlamaIndex."""

import os
import logging
from typing import List, Tuple, Optional
from datetime import datetime

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings
)
from llama_index.core.node_parser import Sen<PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

from config import settings


class CVChatSystem:
    """RAG system for CV-based question answering."""
    
    def __init__(self):
        """Initialize the CV chat system."""
        self.logger = logging.getLogger(__name__)
        self.index: Optional[VectorStoreIndex] = None
        self.query_engine = None
        
        # Configure LlamaIndex settings
        self._configure_llama_index()
        
        # System prompt optimized for CV content
        self.system_prompt = """
You are a professional AI assistant helping users learn about a person's CV/resume. 
You have access to their complete CV content and should provide accurate, helpful responses.

Guidelines:
1. Answer questions directly and professionally
2. Reference specific sections of the CV when relevant
3. If information isn't in the CV, clearly state that
4. Provide context and details when discussing experience or skills
5. Be conversational but maintain professionalism
6. Focus on the person's qualifications, experience, and achievements

Always base your responses on the CV content provided in the context.
"""
    
    def _configure_llama_index(self) -> None:
        """Configure LlamaIndex global settings."""
        try:
            # Configure LLM
            Settings.llm = OpenAI(
                model=settings.llm_model,
                temperature=settings.temperature,
                max_tokens=settings.max_tokens,
                api_key=settings.openai_api_key
            )
            
            # Configure embeddings
            Settings.embed_model = OpenAIEmbedding(
                model=settings.embedding_model,
                api_key=settings.openai_api_key
            )
            
            # Configure text splitter
            Settings.node_parser = SentenceSplitter(
                chunk_size=settings.chunk_size,
                chunk_overlap=settings.chunk_overlap
            )
            
            self.logger.info("LlamaIndex configuration completed")
            
        except Exception as e:
            self.logger.error(f"Failed to configure LlamaIndex: {e}")
            raise
    
    async def initialize(self) -> None:
        """Initialize the RAG system by loading or creating the index."""
        try:
            # Try to load existing index
            if os.path.exists(settings.index_folder_path) and os.listdir(settings.index_folder_path):
                self.logger.info("Loading existing CV index...")
                await self._load_existing_index()
            else:
                self.logger.info("Creating new CV index...")
                await self._create_new_index()
            
            # Create query engine
            self._create_query_engine()
            
            self.logger.info("CV chat system initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize CV chat system: {e}")
            raise
    
    async def _load_existing_index(self) -> None:
        """Load existing vector index from storage."""
        try:
            storage_context = StorageContext.from_defaults(
                persist_dir=settings.index_folder_path
            )
            self.index = load_index_from_storage(storage_context)
            self.logger.info("Successfully loaded existing CV index")
            
        except Exception as e:
            self.logger.warning(f"Failed to load existing index: {e}")
            self.logger.info("Creating new index instead...")
            await self._create_new_index()
    
    async def _create_new_index(self) -> None:
        """Create new vector index from CV documents."""
        try:
            # Check if CV folder exists and has files
            if not os.path.exists(settings.cv_folder_path):
                raise FileNotFoundError(f"CV folder not found: {settings.cv_folder_path}")
            
            cv_files = [f for f in os.listdir(settings.cv_folder_path) if f.lower().endswith('.pdf')]
            if not cv_files:
                raise FileNotFoundError(f"No PDF files found in {settings.cv_folder_path}")
            
            self.logger.info(f"Found {len(cv_files)} CV files: {cv_files}")
            
            # Load documents
            documents = SimpleDirectoryReader(
                input_dir=settings.cv_folder_path,
                required_exts=[".pdf"]
            ).load_data()
            
            if not documents:
                raise ValueError("No documents loaded from CV folder")
            
            self.logger.info(f"Loaded {len(documents)} documents")
            
            # Create index
            self.index = VectorStoreIndex.from_documents(documents)
            
            # Persist index
            self.index.storage_context.persist(persist_dir=settings.index_folder_path)
            
            self.logger.info("Successfully created and persisted new CV index")
            
        except Exception as e:
            self.logger.error(f"Failed to create new index: {e}")
            raise
    
    def _create_query_engine(self) -> None:
        """Create query engine with optimized settings for CV content."""
        try:
            if not self.index:
                raise ValueError("Index not initialized")
            
            # Create retriever
            retriever = VectorIndexRetriever(
                index=self.index,
                similarity_top_k=settings.similarity_top_k
            )
            
            # Create query engine with custom prompt
            self.query_engine = RetrieverQueryEngine.from_args(
                retriever=retriever,
                response_mode=ResponseMode.COMPACT,
                text_qa_template=self._get_qa_template()
            )
            
            self.logger.info("Query engine created successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to create query engine: {e}")
            raise
    
    def _get_qa_template(self) -> str:
        """Get the QA template with system prompt."""
        return f"""{self.system_prompt}

Context information is below:
---------------------
{{context_str}}
---------------------

Given the context information and not prior knowledge, answer the query.
Query: {{query_str}}
Answer: """
    
    async def query(self, message: str, conversation_id: Optional[str] = None) -> Tuple[str, List[str]]:
        """Process a query and return response with sources."""
        try:
            if not self.query_engine:
                raise ValueError("Query engine not initialized")
            
            self.logger.info(f"Processing query: {message[:100]}...")
            
            # Query the index
            response = self.query_engine.query(message)
            
            # Extract sources
            sources = self._extract_sources(response)
            
            self.logger.info(f"Query processed successfully, found {len(sources)} sources")
            
            return str(response), sources
            
        except Exception as e:
            self.logger.error(f"Failed to process query: {e}")
            raise
    
    def _extract_sources(self, response) -> List[str]:
        """Extract source information from query response."""
        sources = []
        
        try:
            if hasattr(response, 'source_nodes'):
                for node in response.source_nodes:
                    if hasattr(node, 'metadata'):
                        # Extract page number and file name
                        page_num = node.metadata.get('page_label', 'unknown page')
                        file_name = node.metadata.get('file_name', 'CV')
                        
                        # Create source reference
                        source = f"{file_name}, page {page_num}"
                        if source not in sources:
                            sources.append(source)
            
        except Exception as e:
            self.logger.warning(f"Failed to extract sources: {e}")
        
        return sources
    
    async def update_index(self) -> Tuple[str, int]:
        """Update the CV index with latest documents."""
        try:
            self.logger.info("Starting CV index update...")
            
            # Remove existing index
            if os.path.exists(settings.index_folder_path):
                import shutil
                shutil.rmtree(settings.index_folder_path)
                os.makedirs(settings.index_folder_path, exist_ok=True)
            
            # Create new index
            await self._create_new_index()
            
            # Recreate query engine
            self._create_query_engine()
            
            # Count processed files
            cv_files = [f for f in os.listdir(settings.cv_folder_path) if f.lower().endswith('.pdf')]
            files_count = len(cv_files)
            
            self.logger.info(f"CV index updated successfully, processed {files_count} files")
            
            return "CV index updated successfully", files_count
            
        except Exception as e:
            self.logger.error(f"Failed to update CV index: {e}")
            raise
    
    def get_suggested_questions(self) -> List[str]:
        """Get suggested questions for CV exploration."""
        return [
            "What programming languages and technologies do you know?",
            "Tell me about your work experience and key achievements",
            "What are your main technical skills and expertise?",
            "What notable projects have you worked on?",
            "What is your educational background?",
            "What certifications or awards do you have?",
            "What industries have you worked in?",
            "What leadership or management experience do you have?",
            "What are your career highlights?",
            "What makes you unique as a candidate?"
        ]
    
    async def health_check(self) -> dict:
        """Perform health check on the system components."""
        checks = {}
        
        try:
            # Check OpenAI API
            try:
                test_embedding = Settings.embed_model.get_text_embedding("test")
                checks["openai_api"] = "connected" if test_embedding else "failed"
            except Exception:
                checks["openai_api"] = "failed"
            
            # Check CV index
            checks["cv_index"] = "loaded" if self.index else "not_loaded"
            
            # Check CV files
            cv_files = [f for f in os.listdir(settings.cv_folder_path) if f.lower().endswith('.pdf')]
            checks["cv_files"] = "found" if cv_files else "not_found"
            checks["cv_files_count"] = len(cv_files)
            
            # Check query engine
            checks["query_engine"] = "ready" if self.query_engine else "not_ready"
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            checks["error"] = str(e)
        
        return checks


# Global instance
cv_chat_system = CVChatSystem()