# CV Chat API - RAG-Powered Resume Assistant

A production-ready RAG (Retrieval-Augmented Generation) system for intelligent CV/resume chat using FastAPI and LlamaIndex. This API enables natural language conversations about CV content with accurate, source-attributed responses.

## 🚀 Features

- **RAG-Powered Chat**: Ask questions about CV content and get intelligent responses
- **Local Persistence**: Vector index stored locally, no external databases required
- **Source Attribution**: Responses include references to specific CV sections
- **Auto-Index Management**: Automatic loading and updating of CV documents
- **Production Ready**: Comprehensive error handling, logging, and health monitoring
- **CORS Enabled**: Ready for frontend integration
- **Docker Support**: Containerized deployment with health checks

## 📋 Prerequisites

- Python 3.11+
- OpenAI API key
- PDF CV/resume files
- Docker (optional, for containerized deployment)

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd cv-chat-api
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

**Required Environment Variables:**
```bash
OPENAI_API_KEY=your_openai_api_key_here
CV_FOLDER_PATH=./cv
INDEX_FOLDER_PATH=./cv_index
API_PORT=8000
CORS_ORIGINS=["http://localhost:3000"]
```

### 3. Install Dependencies

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 4. Add Your CV

```bash
# Create CV folder and add your PDF resume
mkdir cv
cp your-resume.pdf cv/
```

### 5. Run the API

```bash
# Start the server
python main.py

# Or using uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at `http://localhost:8000`

## 🐳 Docker Deployment

### Build and Run

```bash
# Build the image
docker build -t cv-chat-api .

# Run with volume mounts
docker run -d \
  --name cv-chat-api \
  -p 8000:8000 \
  -v $(pwd)/cv:/app/cv \
  -v $(pwd)/cv_index:/app/cv_index \
  -v $(pwd)/.env:/app/.env \
  cv-chat-api
```

### Docker Compose (Recommended)

```yaml
# docker-compose.yml
version: '3.8'

services:
  cv-chat-api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./cv:/app/cv
      - ./cv_index:/app/cv_index
      - ./.env:/app/.env
    environment:
      - PYTHONUNBUFFERED=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

```bash
# Start with docker-compose
docker-compose up -d
```

## 📚 API Documentation

### Endpoints

#### `POST /chat`
Main chat endpoint for CV questions.

**Request:**
```json
{
  "message": "What programming languages do you know?",
  "conversation_id": "optional-uuid"
}
```

**Response:**
```json
{
  "response": "I have experience with Python, JavaScript, React...",
  "sources": ["resume.pdf, page 1", "resume.pdf, page 2"],
  "conversation_id": "550e8400-e29b-41d4-a716-************",
  "confidence": 0.95
}
```

#### `GET /health`
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z",
  "checks": {
    "openai_api": "connected",
    "cv_index": "loaded",
    "cv_files": "found"
  }
}
```

#### `GET /suggested-questions`
Get suggested questions about the CV.

**Response:**
```json
{
  "questions": [
    "What programming languages do you know?",
    "Tell me about your work experience",
    "What are your key skills?"
  ]
}
```

#### `POST /update-cv`
Refresh the CV index with latest documents.

**Response:**
```json
{
  "status": "success",
  "message": "CV index updated successfully",
  "files_processed": 1,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Interactive Documentation

Once running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🧪 Testing the API

### Using cURL

```bash
# Health check
curl -X GET "http://localhost:8000/health"

# Chat request
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What programming languages do you know?"
  }'

# Get suggested questions
curl -X GET "http://localhost:8000/suggested-questions"

# Update CV index
curl -X POST "http://localhost:8000/update-cv"
```

### Using Python

```python
import requests

# Chat with the CV
response = requests.post(
    "http://localhost:8000/chat",
    json={"message": "What is your work experience?"}
)
print(response.json())
```

### Using JavaScript (Frontend Integration)

```javascript
// Chat function for Nuxt.js/Vue.js
async function chatWithCV(message, conversationId = null) {
  try {
    const response = await fetch('http://localhost:8000/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        conversation_id: conversationId
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Chat request failed:', error);
    throw error;
  }
}

// Usage
chatWithCV("What are your technical skills?")
  .then(response => {
    console.log('Response:', response.response);
    console.log('Sources:', response.sources);
  });
```

## ⚙️ Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OPENAI_API_KEY` | Required | OpenAI API key |
| `CV_FOLDER_PATH` | `./cv` | Path to CV PDF files |
| `INDEX_FOLDER_PATH` | `./cv_index` | Vector index storage path |
| `API_PORT` | `8000` | Server port |
| `LOG_LEVEL` | `INFO` | Logging level |
| `CORS_ORIGINS` | `["http://localhost:3000"]` | Allowed CORS origins |
| `CHUNK_SIZE` | `512` | Text chunk size for indexing |
| `CHUNK_OVERLAP` | `50` | Overlap between chunks |
| `SIMILARITY_TOP_K` | `3` | Number of similar chunks to retrieve |
| `MAX_TOKENS` | `1000` | Maximum tokens in response |
| `EMBEDDING_MODEL` | `text-embedding-3-small` | OpenAI embedding model |
| `LLM_MODEL` | `gpt-4` | OpenAI LLM model |
| `TEMPERATURE` | `0.1` | LLM temperature |

### RAG Optimization

The system is optimized for CV content with:
- **Chunk Size**: 512 tokens (optimal for CV sections)
- **Overlap**: 50 tokens (maintains context)
- **Top-K Retrieval**: 3 chunks (balanced relevance)
- **Response Mode**: Compact (concise answers)
- **CV-Specific Prompts**: Tailored for professional content

## 🚀 Production Deployment

### Railway Deployment

1. **Connect Repository**: Link your GitHub repository to Railway
2. **Environment Variables**: Set all required variables in Railway dashboard
3. **Volume Mounts**: Configure persistent storage for `cv_index`
4. **Health Checks**: Railway will use the `/health` endpoint

### Render Deployment

1. **Create Web Service**: Connect your repository
2. **Build Command**: `pip install -r requirements.txt`
3. **Start Command**: `python -m uvicorn main:app --host 0.0.0.0 --port $PORT`
4. **Environment Variables**: Configure in Render dashboard

### Performance Considerations

- **Memory**: ~1GB for typical CV processing
- **Startup Time**: ~5 seconds for index loading
- **Response Time**: <3 seconds per query
- **Concurrent Users**: Supports 10+ simultaneous requests

## 🔧 Development

### Project Structure

```
cv-chat-api/
├── main.py              # FastAPI application
├── cv_chat_system.py    # RAG core logic
├── models.py            # Pydantic models
├── config.py            # Configuration
├── requirements.txt     # Dependencies
├── Dockerfile          # Container config
├── .env.example        # Environment template
├── README.md           # Documentation
├── cv/                 # CV PDF files
├── cv_index/          # Vector index storage
└── logs/              # Application logs
```

### Code Quality

- **Type Hints**: Complete type annotations
- **Error Handling**: Comprehensive exception management
- **Logging**: Structured JSON logging
- **Documentation**: Docstrings for all functions
- **Security**: Non-root Docker user, input validation

### Adding Features

1. **New Endpoints**: Add to `main.py` with proper models
2. **RAG Enhancements**: Modify `cv_chat_system.py`
3. **Configuration**: Update `config.py` and `.env.example`
4. **Models**: Add new Pydantic models to `models.py`

## 🐛 Troubleshooting

### Common Issues

**1. OpenAI API Key Error**
```bash
# Check your API key
echo $OPENAI_API_KEY
# Verify in .env file
cat .env | grep OPENAI_API_KEY
```

**2. No CV Files Found**
```bash
# Check CV folder
ls -la cv/
# Ensure PDF files are present
file cv/*.pdf
```

**3. Index Loading Failed**
```bash
# Remove corrupted index
rm -rf cv_index/
# Restart API to rebuild
python main.py
```

**4. CORS Issues**
```bash
# Update CORS_ORIGINS in .env
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]
```

### Logs and Monitoring

```bash
# View logs
tail -f logs/app.log

# Check health
curl http://localhost:8000/health

# Monitor with Docker
docker logs cv-chat-api -f
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation at `/docs`

---

**Built with ❤️ using FastAPI and LlamaIndex**