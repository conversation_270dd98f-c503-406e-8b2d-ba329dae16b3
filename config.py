"""Configuration module for CV Chat API."""

import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    
    # File Paths
    cv_folder_path: str = Field(default="./cv", description="Path to CV PDF folder")
    index_folder_path: str = Field(default="./cv_index", description="Path to vector index storage")
    
    # Server Configuration
    api_port: int = Field(default=8000, description="API server port")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    
    # RAG Configuration
    chunk_size: int = Field(default=512, description="Text chunk size for indexing")
    chunk_overlap: int = Field(default=50, description="Overlap between text chunks")
    similarity_top_k: int = Field(default=3, description="Number of similar chunks to retrieve")
    max_tokens: int = Field(default=1000, description="Maximum tokens in response")
    
    # Model Configuration
    embedding_model: str = Field(default="text-embedding-3-small", description="OpenAI embedding model")
    llm_model: str = Field(default="gpt-4", description="OpenAI LLM model")
    temperature: float = Field(default=0.1, description="LLM temperature for responses")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Ensure required directories exist
os.makedirs(settings.cv_folder_path, exist_ok=True)
os.makedirs(settings.index_folder_path, exist_ok=True)
os.makedirs("./logs", exist_ok=True)