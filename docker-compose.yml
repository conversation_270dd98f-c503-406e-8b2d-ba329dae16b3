version: '3.8'

services:
  cv-chat-api:
    build: .
    container_name: cv-chat-api
    ports:
      - "8000:8000"
    volumes:
      - ./cv:/app/cv
      - ./cv_index:/app/cv_index
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - cv-chat-network

networks:
  cv-chat-network:
    driver: bridge

volumes:
  cv_data:
    driver: local
  cv_index_data:
    driver: local
  logs_data:
    driver: local